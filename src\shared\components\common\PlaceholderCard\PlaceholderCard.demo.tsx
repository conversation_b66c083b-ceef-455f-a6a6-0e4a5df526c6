import React from 'react';

import { ResponsiveGrid } from '../ResponsiveGrid';
import { Typography } from '../Typography';

import PlaceholderCard from './PlaceholderCard';

/**
 * Demo component showcasing PlaceholderCard usage
 */
const PlaceholderCardDemo: React.FC = () => {
  return (
    <div className="space-y-8 p-6">
      <div>
        <Typography variant="h1" className="mb-4">
          PlaceholderCard Component Demo
        </Typography>
        <Typography variant="body1" className="text-muted-foreground mb-8">
          A reusable component for displaying placeholder content, empty states, or features under development.
        </Typography>
      </div>

      <div className="space-y-6">
        <div>
          <Typography variant="h2" className="mb-4">
            Basic Usage
          </Typography>
          <PlaceholderCard />
        </div>

        <div>
          <Typography variant="h2" className="mb-4">
            Custom Icon and Text
          </Typography>
          <PlaceholderCard
            icon="bar-chart"
            title="Statistics Dashboard"
            description="Advanced analytics and reporting features are coming soon"
            iconSize="xl"
          />
        </div>

        <div>
          <Typography variant="h2" className="mb-4">
            With Translation Keys
          </Typography>
          <PlaceholderCard
            icon="users"
            title="hrm:employee.stats.title"
            description="hrm:employee.stats.description"
            titleFallback="Employee Statistics"
            descriptionFallback="Employee statistics feature is under development"
            namespace="hrm"
          />
        </div>

        <div>
          <Typography variant="h2" className="mb-4">
            Different Icons and Sizes
          </Typography>
          <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3 }}>
            <PlaceholderCard
              icon="calendar"
              iconSize="sm"
              title="Calendar"
              description="Schedule management"
            />
            <PlaceholderCard
              icon="settings"
              iconSize="md"
              title="Settings"
              description="Configuration panel"
            />
            <PlaceholderCard
              icon="chart-line"
              iconSize="lg"
              title="Analytics"
              description="Data visualization"
            />
          </ResponsiveGrid>
        </div>

        <div>
          <Typography variant="h2" className="mb-4">
            Without Card Wrapper
          </Typography>
          <div className="border border-dashed border-gray-300 rounded-lg p-4">
            <PlaceholderCard
              icon="info"
              title="Content Only"
              description="This placeholder doesn't have a card wrapper"
              showCard={false}
            />
          </div>
        </div>

        <div>
          <Typography variant="h2" className="mb-4">
            Custom Styling
          </Typography>
          <PlaceholderCard
            icon="star"
            title="Premium Feature"
            description="Upgrade to access this feature"
            className="p-8 bg-gradient-to-br from-purple-50 to-blue-50 border-2 border-purple-200"
            iconClassName="mx-auto mb-4 text-purple-500"
            titleClassName="text-xl font-bold mb-2 text-purple-800"
            descriptionClassName="text-purple-600"
          />
        </div>

        <div>
          <Typography variant="h2" className="mb-4">
            Custom Children
          </Typography>
          <PlaceholderCard>
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🚀</span>
              </div>
              <Typography variant="h3" className="mb-2">
                Custom Content
              </Typography>
              <Typography variant="body1" className="text-muted-foreground mb-4">
                You can pass custom children to override the default content
              </Typography>
              <button className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                Learn More
              </button>
            </div>
          </PlaceholderCard>
        </div>
      </div>
    </div>
  );
};

export default PlaceholderCardDemo;
