import React from 'react';
import { useTranslation } from 'react-i18next';

import { Card } from '../Card';
import { Icon } from '../Icon';
import { Typography } from '../Typography';

export interface PlaceholderCardProps {
  /**
   * Icon name to display
   */
  icon?: string;
  /**
   * Icon size
   */
  iconSize?: 'sm' | 'md' | 'lg' | 'xl';
  /**
   * Title text or translation key
   */
  title?: string;
  /**
   * Description text or translation key
   */
  description?: string;
  /**
   * Translation namespace for title and description
   */
  namespace?: string;
  /**
   * Default title fallback text
   */
  titleFallback?: string;
  /**
   * Default description fallback text
   */
  descriptionFallback?: string;
  /**
   * Custom className for the card
   */
  className?: string;
  /**
   * Custom className for the content container
   */
  contentClassName?: string;
  /**
   * Custom className for the icon
   */
  iconClassName?: string;
  /**
   * Custom className for the title
   */
  titleClassName?: string;
  /**
   * Custom className for the description
   */
  descriptionClassName?: string;
  /**
   * Whether to show the card wrapper
   */
  showCard?: boolean;
  /**
   * Custom children to render instead of default content
   */
  children?: React.ReactNode;
}

/**
 * Reusable placeholder card component for features under development or empty states
 */
const PlaceholderCard: React.FC<PlaceholderCardProps> = ({
  icon = 'info',
  iconSize = 'lg',
  title,
  description,
  namespace,
  titleFallback = 'Feature Coming Soon',
  descriptionFallback = 'This feature is currently under development',
  className = 'p-6',
  contentClassName = 'text-center py-12',
  iconClassName = 'mx-auto mb-4 text-muted-foreground',
  titleClassName = 'text-lg font-medium mb-2',
  descriptionClassName = 'text-muted-foreground',
  showCard = true,
  children,
}) => {
  const { t } = useTranslation(namespace ? [namespace, 'common'] : ['common']);

  // Get translated text with fallbacks
  const titleText = title ? t(title, titleFallback) : titleFallback;
  const descriptionText = description ? t(description, descriptionFallback) : descriptionFallback;

  const content = children || (
    <div className={contentClassName}>
      <Icon name={icon} size={iconSize} className={iconClassName} />
      <Typography variant="h3" className={titleClassName}>
        {titleText}
      </Typography>
      <Typography variant="body1" className={descriptionClassName}>
        {descriptionText}
      </Typography>
    </div>
  );

  if (!showCard) {
    return <>{content}</>;
  }

  return <Card className={className}>{content}</Card>;
};

export default PlaceholderCard;
