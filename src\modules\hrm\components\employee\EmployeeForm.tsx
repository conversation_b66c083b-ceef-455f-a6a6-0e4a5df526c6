import React from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import {
  Button,
  CollapsibleCard,
  DatePickerFormField,
  Form,
  FormItem,
  Input,
  Select,
  Textarea,
} from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { useFormErrors } from '@/shared/hooks';
import {
  transformEmployeeApiForForm,
  transformEmployeeFormForApi,
} from '@/shared/utils/form-date-utils';

import { loadDepartmentsForAsyncSelect } from '../../hooks/useDepartments';
import { loadEmployeesForAsyncSelect } from '../../hooks/useEmployees';
import { createEmployeeWithUserSchema } from '../../schemas/employee.schema';
import { EmployeeStatus, EmploymentType, MaritalStatus } from '../../types/employee.types';
import { generateEmployeeCode } from '../../utils/employee-code.utils';

// Đ<PERSON>nh nghĩa props cho component
interface EmployeeFormProps {
  initialData?: z.infer<typeof createEmployeeWithUserSchema>;
  onSubmit: (data: z.infer<typeof createEmployeeWithUserSchema>) => void;
  onCancel: () => void;
  isSubmitting?: boolean;
}

/**
 * Form tạo/cập nhật nhân viên
 */
// Import trực tiếp các hàm cần thiết

const EmployeeForm: React.FC<EmployeeFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['hrm', 'common']);
  const isEditMode = !!initialData;

  // Sử dụng hook useFormErrors để quản lý lỗi form
  const { formRef, setFormErrors } = useFormErrors<z.infer<typeof createEmployeeWithUserSchema>>();

  // Helper function to convert string dates to Date objects for form display
  const prepareInitialData = (data: z.infer<typeof createEmployeeWithUserSchema> | undefined) => {
    if (!data) {
      return {
        status: EmployeeStatus.ACTIVE,
        userInfo: {
          username: '',
          password: '',
          email: '',
          fullName: '',
        },
      };
    }
    return transformEmployeeApiForForm(data);
  };

  // Xử lý submit form
  const handleSubmit = (values: unknown) => {
    try {
      // Reset form errors
      setFormErrors({});

      // Type assertion to the correct type
      const employeeValues = values as z.infer<typeof createEmployeeWithUserSchema>;

      // Auto-generate employee code if not provided
      if (!employeeValues.employeeCode && employeeValues.employeeName) {
        employeeValues.employeeCode = generateEmployeeCode(employeeValues.employeeName);
      }

      // Transform Date objects to strings for API compatibility
      const transformedValues = transformEmployeeFormForApi(employeeValues);

      // Call the onSubmit callback
      onSubmit(transformedValues);
    } catch (error) {
      console.error('Error submitting employee form:', error);

      // Check if error has field-specific errors
      if (error && typeof error === 'object' && 'response' in error && error.response) {
        const axiosError = error as {
          response: { data?: { message?: string; errors?: Record<string, string> } };
        };

        // If there are field-specific errors, set them
        if (axiosError.response.data?.errors) {
          setFormErrors(axiosError.response.data.errors);
        }
      }
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold mb-6">
        {isEditMode
          ? t('hrm:employee.form.editTitle', 'Cập nhật thông tin nhân viên')
          : t('hrm:employee.form.createTitle', 'Thêm nhân viên mới')}
      </h2>

      <Form
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ref={formRef as any}
        schema={createEmployeeWithUserSchema}
        onSubmit={handleSubmit}
        className="space-y-6"
        defaultValues={prepareInitialData(initialData)}
      >
        <CollapsibleCard
          title={
            <h3 className="text-lg font-medium p-3">
              {t('hrm:employee.form.userInfo', 'Thông tin tài khoản')}
            </h3>
          }
          defaultOpen={true}
        >
          <div className="space-y-4">
            <FormItem
              name="userInfo.username"
              label={t('hrm:employee.form.username', 'Tên đăng nhập')}
              required
            >
              <Input fullWidth />
            </FormItem>

            <FormItem
              name="userInfo.password"
              label={t('hrm:employee.form.password', 'Mật khẩu')}
              required
            >
              <Input type="password" fullWidth />
            </FormItem>

            <FormItem name="userInfo.email" label={t('hrm:employee.form.email', 'Email')} required>
              <Input type="email" fullWidth />
            </FormItem>

            <FormItem
              name="userInfo.fullName"
              label={t('hrm:employee.form.fullName', 'Họ và tên')}
              required
            >
              <Input fullWidth />
            </FormItem>
          </div>
        </CollapsibleCard>

        <CollapsibleCard
          title={
            <h3 className="text-lg font-medium p-3">
              {t('hrm:employee.form.basicInfo', 'Thông tin cơ bản')}
            </h3>
          }
          defaultOpen={false}
        >
          <div className="space-y-4">
            <FormItem
              name="employeeName"
              label={t('hrm:employee.form.employeeName', 'Tên nhân viên')}
              required
            >
              <Input fullWidth />
            </FormItem>

            <FormItem name="jobTitle" label={t('hrm:employee.form.jobTitle', 'Chức danh')}>
              <Input fullWidth />
            </FormItem>

            <FormItem name="jobLevel" label={t('hrm:employee.form.jobLevel', 'Cấp bậc')}>
              <Input fullWidth />
            </FormItem>

            <FormItem name="departmentId" label={t('hrm:employee.form.departmentId', 'Phòng ban')}>
              <AsyncSelectWithPagination loadOptions={loadDepartmentsForAsyncSelect} fullWidth />
            </FormItem>

            <FormItem name="managerId" label={t('hrm:employee.form.managerId', 'Quản lý')}>
              <AsyncSelectWithPagination loadOptions={loadEmployeesForAsyncSelect} fullWidth />
            </FormItem>

            <FormItem
              name="employmentType"
              label={t('hrm:employee.form.employmentType', 'Loại hợp đồng')}
            >
              <Select
                options={Object.values(EmploymentType).map(type => ({
                  value: type,
                  label: t(`hrm:employee.employmentType.${type}`, type),
                }))}
                fullWidth
              />
            </FormItem>

            <FormItem name="status" label={t('hrm:employee.form.status', 'Trạng thái')}>
              <Select
                options={Object.values(EmployeeStatus).map(status => ({
                  value: status,
                  label: t(`hrm:employee.status.${status}`, status),
                }))}
                fullWidth
              />
            </FormItem>

            <FormItem name="hireDate" label={t('hrm:employee.form.hireDate', 'Ngày vào làm')}>
              <DatePickerFormField />
            </FormItem>

            <FormItem
              name="probationEndDate"
              label={t('hrm:employee.form.probationEndDate', 'Ngày kết thúc thử việc')}
            >
              <DatePickerFormField />
            </FormItem>
          </div>
        </CollapsibleCard>

        <CollapsibleCard
          title={
            <h3 className="text-lg font-medium p-3">
              {t('hrm:employee.form.personalInfo', 'Thông tin cá nhân')}
            </h3>
          }
          defaultOpen={false}
        >
          <div className="space-y-4">
            <FormItem name="dateOfBirth" label={t('hrm:employee.form.dateOfBirth', 'Ngày sinh')}>
              <DatePickerFormField />
            </FormItem>

            <FormItem name="gender" label={t('hrm:employee.form.gender', 'Giới tính')}>
              <Select
                options={[
                  { value: 'male', label: t('hrm:employee.gender.male', 'Nam') },
                  { value: 'female', label: t('hrm:employee.gender.female', 'Nữ') },
                  { value: 'other', label: t('hrm:employee.gender.other', 'Khác') },
                ]}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="maritalStatus"
              label={t('hrm:employee.form.maritalStatus', 'Tình trạng hôn nhân')}
            >
              <Select
                options={Object.values(MaritalStatus).map(status => ({
                  value: status,
                  label: t(`hrm:employee.maritalStatus.${status}`, status),
                }))}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="numberOfDependents"
              label={t('hrm:employee.form.numberOfDependents', 'Số người phụ thuộc')}
            >
              <Input type="number" fullWidth />
            </FormItem>
          </div>
        </CollapsibleCard>

        <CollapsibleCard
          title={
            <h3 className="text-lg font-medium p-3">
              {t('hrm:employee.form.contactInfo', 'Thông tin liên hệ')}
            </h3>
          }
          defaultOpen={false}
        >
          <div className="space-y-4">
            <FormItem name="address" label={t('hrm:employee.form.address', 'Địa chỉ')}>
              <Input fullWidth />
            </FormItem>

            <FormItem name="city" label={t('hrm:employee.form.city', 'Thành phố')}>
              <Input fullWidth />
            </FormItem>

            <FormItem name="state" label={t('hrm:employee.form.state', 'Tỉnh/Bang')}>
              <Input fullWidth />
            </FormItem>

            <FormItem name="country" label={t('hrm:employee.form.country', 'Quốc gia')}>
              <Input fullWidth />
            </FormItem>

            <FormItem name="postalCode" label={t('hrm:employee.form.postalCode', 'Mã bưu điện')}>
              <Input fullWidth />
            </FormItem>
          </div>
        </CollapsibleCard>

        <CollapsibleCard
          title={
            <h3 className="text-lg font-medium p-3">
              {t('hrm:employee.form.emergencyContact', 'Liên hệ khẩn cấp')}
            </h3>
          }
          defaultOpen={false}
        >
          <div className="space-y-4">
            <FormItem
              name="emergencyContactName"
              label={t('hrm:employee.form.emergencyContactName', 'Tên liên hệ')}
            >
              <Input fullWidth />
            </FormItem>

            <FormItem
              name="emergencyContactPhone"
              label={t('hrm:employee.form.emergencyContactPhone', 'Số điện thoại')}
            >
              <Input fullWidth />
            </FormItem>

            <FormItem
              name="emergencyContactRelationship"
              label={t('hrm:employee.form.emergencyContactRelationship', 'Mối quan hệ')}
            >
              <Input fullWidth />
            </FormItem>
          </div>
        </CollapsibleCard>

        <CollapsibleCard
          title={
            <h3 className="text-lg font-medium p-3">{t('hrm:employee.form.notes', 'Ghi chú')}</h3>
          }
          defaultOpen={false}
        >
          <FormItem name="notes" label={t('hrm:employee.form.notes', 'Ghi chú')}>
            <Textarea rows={4} fullWidth />
          </FormItem>
        </CollapsibleCard>

        <div className="flex justify-end space-x-4 mt-8">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? t('common:saving', 'Đang lưu...') : t('common:save', 'Lưu')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default EmployeeForm;
